import fs from 'fs'

// Function to offset timecodes in an .ass file
function offsetTimecodes(inputFile, outputFile, offset) {
    const content = fs.readFileSync(inputFile, 'utf8');
    const lines = content.split('\n');

    const offsetMs = parseTimecode(offset);

    const offsetLines = lines.map(line => {
        if (line.startsWith('Dialogue:')) {
            const parts = line.split(',');
            if (parts.length >= 2) {
                const startTime = parseTimecode(parts[1]);
                const endTime = parseTimecode(parts[2]);

                const offsetStart = formatTimecode(startTime + offsetMs);
                const offsetEnd = formatTimecode(endTime + offsetMs);

                parts[1] = offsetStart;
                parts[2] = offsetEnd;

                return parts.join(',');
            }
        }
        return line;
    });

    fs.writeFileSync(outputFile, offsetLines.join('\n'));
    console.log(`Offset timecodes saved to ${outputFile}`);
}

// Helper function to parse timecode string to milliseconds
function parseTimecode(timecode) {
    const [hours, minutes, seconds] = timecode.split(':');
    return (parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds)) * 1000;
}

// Helper function to format milliseconds to timecode string
function formatTimecode(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = (totalSeconds % 60) + (ms % 1000) / 1000;
    return `${padZero(hours)}:${padZero(minutes)}:${padZero(seconds.toFixed(2))}`;
}

// Helper function to pad single digits with leading zero
function padZero(num) {
    return num.toString().padStart(2, '0');
}

// Example usage
offsetTimecodes('app/normalized.ass', 'app/final.ass', '00:01:30.00');