// @ts-check

/**
 * Subtitle Post-Processing and Translation Application
 *
 * This script performs the following tasks:
 *
 * 1. Processes translated subtitle files (.txt) in a specified input directory.
 * 2. Separates speaker information from dialogue lines in each subtitle file.
 * 3. Removes empty newlines from the processed content.
 * 4. Saves the modified subtitle files to a specified output directory.
 * 5. Deletes the original translation files after processing.
 * 6. Implements robust error handling and logging mechanisms:
 *    - Logs information, errors, uncaught exceptions, and unhandled rejections to separate files.
 *    - Uses color-coded console output for better readability and error distinction.
 * 7. Manages file operations for reading input files and writing processed outputs.
 * 8. Implements a graceful shutdown process to ensure proper resource cleanup.
 * 9. Utilizes environment variables for configuration (loaded via dotenv).
 * 10. Provides utility functions for directory creation and timestamp generation.
 * 11. Overrides console.info and console.error methods to enhance logging capabilities.
 * 12. Implements handlers for uncaught exceptions and unhandled rejections.
 * 13. Sets up a SIGINT handler for graceful shutdown on process interruption.
 * 14. Initiates a subsequent script (2applyTranslation.js) to apply translations to the processed files.
 * 15. Uses child_process to execute the translation application script.
 * 16. Manages the execution state of the script using an IS_RUNNING_FILE.
 *
 * The script is designed as a crucial step in the subtitle translation pipeline,
 * focusing on preparing the translated subtitles for final processing and application.
 * It ensures efficient operation, error recovery, and detailed logging for troubleshooting,
 * while also managing the transition to the next stage of the translation process.
 */

import { config } from "dotenv";
config();
import process from "process";
import fs from "fs";
import path from "path";
import * as cp from "child_process";

const IS_RUNNING_FILE = "app/isRunning.txt";

/**
 * @constant {Object} COLORS - ANSI color codes for formatting console output.
 */
const COLORS = {
  RESET: "\x1b[0m",
  BLACK: "\x1b[30m",
  RED: "\x1b[31m",
  GREEN: "\x1b[32m",
  YELLOW: "\x1b[33m",
  BLUE: "\x1b[34m",
  MAGENTA: "\x1b[35m",
  CYAN: "\x1b[36m",
  WHITE: "\x1b[37m",
  GRAY: "\x1b[90m",
  DIM: "\x1b[2m",
  BG_BLACK: "\x1b[40m",
  BG_RED: "\x1b[41m",
  BG_GREEN: "\x1b[42m",
  BG_YELLOW: "\x1b[43m",
  BG_BLUE: "\x1b[44m",
  BG_MAGENTA: "\x1b[45m",
  BG_CYAN: "\x1b[46m",
  BG_WHITE: "\x1b[47m",
};

const logsDir = "app/logs";
createDirectoryIfNotExists(logsDir);

const infoLogStream = createWriteStream(path.join(logsDir, "info.log"));
const errorLogStream = createWriteStream(path.join(logsDir, "error.log"));
const exceptionLogStream = createWriteStream(path.join(logsDir, "exception.log"));
const rejectionLogStream = createWriteStream(path.join(logsDir, "rejection.log"));

setupLogging();
setupUncaughtErrorHandling();

const inputDirectory = "app/3replace/withActors";
const outputDirectory = "app/3replace/clean";

main();

/**
 * Main function to process files and start separating actors from dialogue.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs during execution.
 */
async function main() {
  try {
    await processFiles(inputDirectory, outputDirectory);
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Proceeding to the <APPLYING TRANSLATION> step...${COLORS.RESET}`);
    startApplyingTranslation();
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] An error occurred during execution: ${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * Process .txt files in the input directory and save them (as .txt) to the output directory.

 * @param {string} inputDirectory - Directory path for input files.
 * @param {string} outputDirectory - Directory path for output files.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs while processing files.
 */
async function processFiles(inputDirectory, outputDirectory) {
  try {
    const files = fs.readdirSync(inputDirectory).filter((file) => file.endsWith(".txt"));
    for (const file of files) {
      await processFile(file, inputDirectory, outputDirectory);
    }
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing files: ${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * Process a single file by separating speaker from lines and saving the modified file to the output directory.
 * @param {string} file - Name of the file to process.
 * @param {string} inputDirectory - Directory path for input files.
 * @param {string} outputDirectory - Directory path for output files.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs while processing the file.
 */
async function processFile(file, inputDirectory, outputDirectory) {
  const inputFilePath = path.join(inputDirectory, file);
  const outputFilePath = path.join(outputDirectory, file);
  try {
    await separateSpeakerFromLine(inputFilePath, outputFilePath);
    if (fs.existsSync(inputFilePath)) {
      fs.unlinkSync(inputFilePath);
      console.info(`${COLORS.GRAY}[INFO] Deleted original translation file: ${inputFilePath}${COLORS.RESET}`);
    }
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing file: ${file}. ${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * Remove empty newlines from a string.
 * @param {string} content - The content to process.
 * @returns {string} The content with empty newlines removed.
 */
function removeEmptyNewlines(content) {
  return content
    .split("\n")
    .filter((line) => line.trim() !== "")
    .join("\n");
}

/**
 * Separate speaker from each line in the input file, remove empty newlines, and save the modified lines to the output file.
 * @param {string} inputFile - Path to the input file.
 * @param {string} outputFile - Path to the output file.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs while separating speaker from lines or removing empty newlines.
 */
async function separateSpeakerFromLine(inputFile, outputFile) {
  try {
    console.info(`${COLORS.GRAY}[INFO] Processing file: ${inputFile}...${COLORS.RESET}`);
    const data = fs.readFileSync(inputFile, "utf8");
    const dialogues = data.split("\n");
    const lines = dialogues
      .map((dialogue) => {
        if (dialogue.includes(" | ")) {
          const parts = dialogue.split(" | ");
          return parts[1];
        }
        return dialogue;
      })
      .filter(Boolean);

    const processedContent = removeEmptyNewlines(lines.join("\n"));
    console.info(`${COLORS.GRAY}[INFO] Processing completed for: ${inputFile}${COLORS.RESET}`);
    fs.writeFileSync(outputFile, processedContent);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing file: ${inputFile}.${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * Create a directory if it doesn't exist.
 * @param {string} directory - Directory path to create.
 * @returns {void}
 */
function createDirectoryIfNotExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory);
  }
}

/**
 * Create a write stream for a file.
 * @param {string} filePath - Path to the file.
 * @returns {fs.WriteStream} Write stream for the file.
 */
function createWriteStream(filePath) {
  return fs.createWriteStream(filePath, { flags: "a" });
}

/**
 * Override the console.info method to log messages to the console and info log file.
 * @param {fs.WriteStream} logStream - Write stream for the info log file.
 * @returns {void}
 */
function overrideConsoleLog(logStream) {
  const originalLog = console.info;
  console.info = (...args) => {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] ${args.join(" ")}\n`;
    originalLog(...args);
    logStream.write(logMessage);
  };
}

/**
 * Override the console.error method to log error messages to the console and error log file.
 * @param {fs.WriteStream} errorStream - Write stream for the error log file.
 * @returns {void}
 */
function overrideConsoleError(errorStream) {
  const originalError = console.error;
  console.error = (...args) => {
    const timestamp = getTimestamp();
    const errorMessage = cleanErrorMessage(`[${timestamp}] ${args.join(" ")}\n`);
    originalError(...args);
    errorStream.write(errorMessage);
  };
}

/**
 * Handle uncaught exceptions and log the exception details to the console and exception log file.
 * @param {Error} err - The uncaught exception error object.
 * @returns {Promise<void>}
 */
async function handleUncaughtException(err) {
  const errorMessage = cleanErrorMessage(err.toString());
  const timestamp = getTimestamp();
  const exceptionMessage = `[${timestamp}] ${err.stack}\n`;
  console.error(exceptionMessage);
  exceptionLogStream.write(exceptionMessage);
  process.exit(1);
}

/**
 * Handle unhandled promise rejections and log the rejection details to the console and rejection log file.
 * @param {*} reason - The reason for the promise rejection.
 * @param {Promise} promise - The rejected promise.
 * @returns {Promise<void>}
 */
async function handleUnhandledRejection(reason, promise) {
  const reasonMessage = cleanErrorMessage(reason.toString());
  const timestamp = getTimestamp();
  const rejectionMessage = `[${timestamp}]\n${reason.stack}\n`;
  console.error(rejectionMessage);
  rejectionLogStream.write(rejectionMessage);
  process.exit(1);
}

/**
 * Handle graceful shutdown by stopping the script, logging the shutdown message, and closing log streams.
 * @returns {void}
 */
function handleGracefulShutdown() {
  stopRunning();
  const timestamp = getTimestamp();
  const shutdownMessage = `[${timestamp}] Graceful shutdown\n`;
  console.info(shutdownMessage);
  infoLogStream.write(shutdownMessage);
  infoLogStream.end();
  errorLogStream.end();
  exceptionLogStream.end();
  rejectionLogStream.end(() => {
    process.exit(0);
  });
}

/**
 * Stop the script by updating the IS_RUNNING_FILE.
 * @returns {void}
 */
function stopRunning() {
  fs.writeFileSync(IS_RUNNING_FILE, "false");
}

/**
 * Get the current timestamp in ISO format.
 * @returns {string} Current timestamp in ISO format.
 */
function getTimestamp() {
  const now = new Date();
  return now.toISOString();
}

/**
 * Clean the error message by removing ANSI color codes.
 * @param {string} errorMessage - The error message to clean.
 * @returns {string} The cleaned error message.
 */
function cleanErrorMessage(errorMessage) {
  // @ts-ignore
  return errorMessage.replace(/\[90m/g, "").replace(/\[36m/g, "").replace(/\[37m/g, "").replace(/\[46m/g, "").replace(/\[30m/g, "").replace(/\[0m/g, "").replace(/\[31m/g, "").replaceAll("", "");
}

/**
 * Set up uncaught error handling by attaching event listeners for uncaught exceptions, unhandled rejections, and SIGINT signal.
 * @returns {void}
 */
function setupUncaughtErrorHandling() {
  process.on("uncaughtException", handleUncaughtException);
  process.on("unhandledRejection", handleUnhandledRejection);
  process.on("SIGINT", handleGracefulShutdown);
}

/**
 * Set up logging by overriding console.info and console.error methods.
 * @returns {void}
 */
function setupLogging() {
  overrideConsoleLog(infoLogStream);
  overrideConsoleError(errorLogStream);
}

/**
 * Start the translation application process by executing the specified script.
 * @returns {void}
 * @throws {Error} If an error occurs during the translation application process.
 */
function startApplyingTranslation() {
  try {
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}INFO] Starting translation application process...${COLORS.RESET}`);
    cp.execSync("node app/3replace/2applyTranslation.js", { stdio: "inherit" });
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Translation application process completed successfully.${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error in translation application process: ${error.message}${COLORS.RESET}`);
    process.exit(1); // Exit the entire application if translation application process fails
  }
}
