/**
 * Claude 4 Verification System CLI Tool
 * 
 * Command-line interface for managing the Claude 4 verification system,
 * viewing statistics, and exporting improvement data.
 */

import { config } from 'dotenv';
config();
import fs from 'fs';
import path from 'path';
import { getVerificationStats } from './claude4-verifier.js';
import { getImprovementStats, generateFeedbackForClaude35, exportImprovementData } from './improvement-tracker.js';
import { CLAUDE4_CONFIG } from './config.js';

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  BG_BLUE: '\x1b[44m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
};

const commands = {
  'status': showStatus,
  'stats': showStatistics,
  'feedback': showFeedback,
  'export': exportData,
  'config': showConfig,
  'help': showHelp
};

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0] || 'help';
const options = parseOptions(args.slice(1));

// Execute command
if (commands[command]) {
  try {
    await commands[command](options);
  } catch (error) {
    console.error(`${COLORS.RED}Error executing command '${command}': ${error.message}${COLORS.RESET}`);
    process.exit(1);
  }
} else {
  console.error(`${COLORS.RED}Unknown command: ${command}${COLORS.RESET}`);
  showHelp();
  process.exit(1);
}

/**
 * Show system status
 * @param {Object} options - Command options
 */
async function showStatus(options) {
  console.log(`${COLORS.BG_BLUE}${COLORS.WHITE} Claude 4 Verification System Status ${COLORS.RESET}\n`);
  
  console.log(`${COLORS.CYAN}Configuration:${COLORS.RESET}`);
  console.log(`  Verification Enabled: ${CLAUDE4_CONFIG.VERIFICATION_ENABLED ? '✅' : '❌'}`);
  console.log(`  Verification Mode: ${COLORS.YELLOW}${CLAUDE4_CONFIG.VERIFICATION_MODE}${COLORS.RESET}`);
  console.log(`  Sample Rate: ${COLORS.YELLOW}${(CLAUDE4_CONFIG.SAMPLE_RATE * 100).toFixed(1)}%${COLORS.RESET}`);
  console.log(`  Tracking Improvements: ${CLAUDE4_CONFIG.TRACK_IMPROVEMENTS ? '✅' : '❌'}`);
  console.log(`  Logging Results: ${CLAUDE4_CONFIG.LOG_VERIFICATION_RESULTS ? '✅' : '❌'}`);
  
  console.log(`\n${COLORS.CYAN}Quality Thresholds:${COLORS.RESET}`);
  console.log(`  Accuracy: ${COLORS.YELLOW}${CLAUDE4_CONFIG.MIN_ACCURACY_SCORE}${COLORS.RESET}`);
  console.log(`  Fluency: ${COLORS.YELLOW}${CLAUDE4_CONFIG.MIN_FLUENCY_SCORE}${COLORS.RESET}`);
  console.log(`  Cultural: ${COLORS.YELLOW}${CLAUDE4_CONFIG.MIN_CULTURAL_SCORE}${COLORS.RESET}`);
  
  // Check log files
  console.log(`\n${COLORS.CYAN}Log Files:${COLORS.RESET}`);
  const verificationLogExists = fs.existsSync('app/logs/verification.log');
  const improvementLogExists = fs.existsSync(CLAUDE4_CONFIG.IMPROVEMENT_LOG_PATH);
  
  console.log(`  Verification Log: ${verificationLogExists ? '✅' : '❌'} app/logs/verification.log`);
  console.log(`  Improvement Log: ${improvementLogExists ? '✅' : '❌'} ${CLAUDE4_CONFIG.IMPROVEMENT_LOG_PATH}`);
  
  if (verificationLogExists) {
    const stats = fs.statSync('app/logs/verification.log');
    console.log(`    Size: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`    Modified: ${stats.mtime.toLocaleString()}`);
  }
}

/**
 * Show verification and improvement statistics
 * @param {Object} options - Command options
 */
async function showStatistics(options) {
  console.log(`${COLORS.BG_GREEN}${COLORS.WHITE} Claude 4 Verification Statistics ${COLORS.RESET}\n`);
  
  try {
    const verificationStats = getVerificationStats();
    const improvementStats = getImprovementStats();
    
    console.log(`${COLORS.CYAN}Verification Summary:${COLORS.RESET}`);
    console.log(`  Total Verifications: ${COLORS.YELLOW}${verificationStats.totalVerifications}${COLORS.RESET}`);
    console.log(`  Issues Found: ${COLORS.YELLOW}${verificationStats.issuesFound}${COLORS.RESET}`);
    console.log(`  Success Rate: ${COLORS.YELLOW}${(verificationStats.successRate * 100).toFixed(1)}%${COLORS.RESET}`);
    
    if (verificationStats.averageScores) {
      console.log(`\n${COLORS.CYAN}Average Scores:${COLORS.RESET}`);
      console.log(`  Accuracy: ${COLORS.YELLOW}${verificationStats.averageScores.accuracy.toFixed(2)}${COLORS.RESET}`);
      console.log(`  Fluency: ${COLORS.YELLOW}${verificationStats.averageScores.fluency.toFixed(2)}${COLORS.RESET}`);
      console.log(`  Cultural: ${COLORS.YELLOW}${verificationStats.averageScores.cultural.toFixed(2)}${COLORS.RESET}`);
    }
    
    console.log(`\n${COLORS.CYAN}Improvement Tracking:${COLORS.RESET}`);
    console.log(`  Total Improvements: ${COLORS.YELLOW}${improvementStats.totalImprovements}${COLORS.RESET}`);
    console.log(`  Improvement Rate: ${COLORS.YELLOW}${(improvementStats.improvementRate * 100).toFixed(1)}%${COLORS.RESET}`);
    
    if (improvementStats.mostCommonIssues?.length > 0) {
      console.log(`\n${COLORS.CYAN}Most Common Issues:${COLORS.RESET}`);
      improvementStats.mostCommonIssues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.type}: ${COLORS.YELLOW}${issue.count}${COLORS.RESET} occurrences`);
      });
    }
    
    if (improvementStats.severityDistribution) {
      console.log(`\n${COLORS.CYAN}Issue Severity Distribution:${COLORS.RESET}`);
      Object.entries(improvementStats.severityDistribution).forEach(([severity, count]) => {
        const stars = '★'.repeat(parseInt(severity));
        console.log(`  ${stars} (${severity}): ${COLORS.YELLOW}${count}${COLORS.RESET}`);
      });
    }
    
  } catch (error) {
    console.log(`${COLORS.GRAY}No statistics available yet. Run some translations with verification enabled first.${COLORS.RESET}`);
  }
}

/**
 * Show feedback for Claude 3.5
 * @param {Object} options - Command options
 */
async function showFeedback(options) {
  const hours = options.hours || 24;
  console.log(`${COLORS.BG_YELLOW}${COLORS.WHITE} Claude 3.5 Learning Feedback (Last ${hours}h) ${COLORS.RESET}\n`);
  
  try {
    const feedback = generateFeedbackForClaude35(hours);
    
    console.log(`${COLORS.CYAN}Summary:${COLORS.RESET}`);
    console.log(`  Total Suggestions: ${COLORS.YELLOW}${feedback.totalSuggestions}${COLORS.RESET}`);
    console.log(`  Period: ${COLORS.YELLOW}${feedback.period}${COLORS.RESET}`);
    
    if (feedback.issueBreakdown && Object.keys(feedback.issueBreakdown).length > 0) {
      console.log(`\n${COLORS.CYAN}Issue Breakdown:${COLORS.RESET}`);
      Object.entries(feedback.issueBreakdown).forEach(([type, count]) => {
        console.log(`  ${type}: ${COLORS.YELLOW}${count}${COLORS.RESET}`);
      });
    }
    
    if (feedback.priorityIssues?.length > 0) {
      console.log(`\n${COLORS.CYAN}Priority Issues:${COLORS.RESET}`);
      feedback.priorityIssues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.description}`);
        console.log(`     Frequency: ${COLORS.YELLOW}${issue.frequency}${COLORS.RESET}, Avg Severity: ${COLORS.YELLOW}${issue.avgSeverity}${COLORS.RESET}`);
      });
    }
    
    if (feedback.learningRecommendations?.length > 0) {
      console.log(`\n${COLORS.CYAN}Learning Recommendations:${COLORS.RESET}`);
      feedback.learningRecommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }
    
    if (feedback.positivePatterns?.length > 0) {
      console.log(`\n${COLORS.GREEN}Positive Patterns:${COLORS.RESET}`);
      feedback.positivePatterns.forEach((pattern, index) => {
        console.log(`  ${index + 1}. ${pattern}`);
      });
    }
    
  } catch (error) {
    console.log(`${COLORS.GRAY}No feedback available yet. Run some translations with verification enabled first.${COLORS.RESET}`);
  }
}

/**
 * Export improvement data
 * @param {Object} options - Command options
 */
async function exportData(options) {
  const format = options.format || 'json';
  const outputFile = options.output || `claude4-improvements-${new Date().toISOString().split('T')[0]}.${format}`;
  
  console.log(`${COLORS.CYAN}Exporting improvement data to ${outputFile}...${COLORS.RESET}`);
  
  try {
    const data = await exportImprovementData(format);
    fs.writeFileSync(outputFile, data);
    
    const stats = fs.statSync(outputFile);
    console.log(`${COLORS.GREEN}✅ Export completed successfully!${COLORS.RESET}`);
    console.log(`  File: ${outputFile}`);
    console.log(`  Size: ${(stats.size / 1024).toFixed(2)} KB`);
    
  } catch (error) {
    console.error(`${COLORS.RED}Export failed: ${error.message}${COLORS.RESET}`);
  }
}

/**
 * Show current configuration
 * @param {Object} options - Command options
 */
async function showConfig(options) {
  console.log(`${COLORS.BG_BLUE}${COLORS.WHITE} Claude 4 Configuration ${COLORS.RESET}\n`);
  
  const configEntries = Object.entries(CLAUDE4_CONFIG);
  configEntries.forEach(([key, value]) => {
    const displayValue = typeof value === 'string' && value.length > 50 
      ? value.substring(0, 47) + '...' 
      : value;
    console.log(`  ${key}: ${COLORS.YELLOW}${displayValue}${COLORS.RESET}`);
  });
  
  console.log(`\n${COLORS.GRAY}Configuration is loaded from environment variables and defaults.${COLORS.RESET}`);
  console.log(`${COLORS.GRAY}Edit the .env file to modify these settings.${COLORS.RESET}`);
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`${COLORS.BG_BLUE}${COLORS.WHITE} Claude 4 Verification System CLI ${COLORS.RESET}\n`);
  
  console.log(`${COLORS.CYAN}Usage:${COLORS.RESET}`);
  console.log(`  node claude4-cli.js <command> [options]\n`);
  
  console.log(`${COLORS.CYAN}Commands:${COLORS.RESET}`);
  console.log(`  ${COLORS.YELLOW}status${COLORS.RESET}     Show system status and configuration`);
  console.log(`  ${COLORS.YELLOW}stats${COLORS.RESET}      Show verification and improvement statistics`);
  console.log(`  ${COLORS.YELLOW}feedback${COLORS.RESET}   Show learning feedback for Claude 3.5`);
  console.log(`  ${COLORS.YELLOW}export${COLORS.RESET}     Export improvement data`);
  console.log(`  ${COLORS.YELLOW}config${COLORS.RESET}     Show current configuration`);
  console.log(`  ${COLORS.YELLOW}help${COLORS.RESET}       Show this help message\n`);
  
  console.log(`${COLORS.CYAN}Options:${COLORS.RESET}`);
  console.log(`  ${COLORS.YELLOW}--hours <n>${COLORS.RESET}     Hours to look back for feedback (default: 24)`);
  console.log(`  ${COLORS.YELLOW}--format <fmt>${COLORS.RESET}  Export format: json, csv (default: json)`);
  console.log(`  ${COLORS.YELLOW}--output <file>${COLORS.RESET} Output file for export\n`);
  
  console.log(`${COLORS.CYAN}Examples:${COLORS.RESET}`);
  console.log(`  node claude4-cli.js status`);
  console.log(`  node claude4-cli.js feedback --hours 48`);
  console.log(`  node claude4-cli.js export --format csv --output improvements.csv`);
}

/**
 * Parse command line options
 * @param {string[]} args - Command line arguments
 * @returns {Object} Parsed options
 */
function parseOptions(args) {
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i]?.replace(/^--/, '');
    const value = args[i + 1];
    
    if (key && value) {
      // Convert numeric values
      if (!isNaN(value)) {
        options[key] = parseFloat(value);
      } else {
        options[key] = value;
      }
    }
  }
  
  return options;
}
