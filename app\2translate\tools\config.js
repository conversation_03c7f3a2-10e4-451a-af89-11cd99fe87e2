/**
 * Configuration module for AI models used in translation pipeline
 * Supports configurable models for different tasks: translation, verification, and improvement
 */

// Available Claude models
export const AVAILABLE_MODELS = {
  // Claude 3.5 Sonnet variants
  'claude-3-5-sonnet-20241022': {
    name: 'Claude 3.5 Sonnet (Latest)',
    maxTokens: 8192,
    recommendedFor: ['translation', 'improvement'],
    description: 'Latest Claude 3.5 Sonnet with improved performance'
  },
  'claude-3-5-sonnet-20240620': {
    name: 'Claude 3.5 Sonnet (June)',
    maxTokens: 8192,
    recommendedFor: ['translation', 'improvement'],
    description: 'Previous version of Claude 3.5 Sonnet'
  },

  // Claude 3 Opus
  'claude-3-opus-20240229': {
    name: 'Claude 3 Opus',
    maxTokens: 4096,
    recommendedFor: ['verification', 'improvement'],
    description: 'Most capable Claude 3 model, excellent for complex analysis'
  },

  // Claude 3 Sonnet
  'claude-3-sonnet-20240229': {
    name: 'Claude 3 Sonnet',
    maxTokens: 4096,
    recommendedFor: ['translation', 'verification'],
    description: 'Balanced performance and speed'
  },

  // Claude 3 Haiku
  'claude-3-haiku-20240307': {
    name: '<PERSON> 3 <PERSON>ku',
    maxTokens: 4096,
    recommendedFor: ['verification'],
    description: 'Fastest Claude 3 model, good for quick verification'
  },

  // Claude 4 (when available)
  'claude-sonnet-4-20250514': {
    name: 'Claude 4 Sonnet',
    maxTokens: 8192,
    recommendedFor: ['verification', 'improvement'],
    description: 'Latest Claude 4 with tool use capabilities'
  }
};

// Model configuration for different tasks
export const MODEL_CONFIG = {
  // Translation model (Claude 3.5 for main translation work)
  TRANSLATION: {
    MODEL: process.env.TRANSLATION_MODEL || "claude-3-5-sonnet-20241022",
    MAX_TOKENS: parseInt(process.env.TRANSLATION_MAX_TOKENS) || 8192,
    TEMPERATURE: parseFloat(process.env.TRANSLATION_TEMPERATURE) || 1.0,
    DESCRIPTION: "Model used for main translation tasks"
  },

  // Verification model (Claude 4 for verification with tools)
  VERIFICATION: {
    MODEL: process.env.VERIFICATION_MODEL || "claude-sonnet-4-20250514",
    MAX_TOKENS: parseInt(process.env.VERIFICATION_MAX_TOKENS) || 8192,
    TEMPERATURE: parseFloat(process.env.VERIFICATION_TEMPERATURE) || 0.3,
    DESCRIPTION: "Model used for translation verification and quality analysis"
  },

  // Improvement model (Claude 3.5 for fixing issues)
  IMPROVEMENT: {
    MODEL: process.env.IMPROVEMENT_MODEL || "claude-3-5-sonnet-20241022",
    MAX_TOKENS: parseInt(process.env.IMPROVEMENT_MAX_TOKENS) || 8192,
    TEMPERATURE: parseFloat(process.env.IMPROVEMENT_TEMPERATURE) || 0.7,
    DESCRIPTION: "Model used for improving translations based on verification feedback"
  }
};

export const CLAUDE4_CONFIG = {
  // Claude 4 API configuration (now uses MODEL_CONFIG.VERIFICATION)
  MODEL: MODEL_CONFIG.VERIFICATION.MODEL,
  MAX_TOKENS: MODEL_CONFIG.VERIFICATION.MAX_TOKENS,
  TEMPERATURE: MODEL_CONFIG.VERIFICATION.TEMPERATURE,
  
  // Verification settings
  VERIFICATION_ENABLED: process.env.CLAUDE4_VERIFICATION_ENABLED === 'true',
  VERIFICATION_MODE: process.env.CLAUDE4_VERIFICATION_MODE || 'full', // 'full', 'sample', 'disabled'
  SAMPLE_RATE: parseFloat(process.env.CLAUDE4_SAMPLE_RATE) || 0.2, // 20% of chunks when in sample mode
  
  // Quality threshold - single condition based on average of all scores
  MIN_AVERAGE_SCORE: parseFloat(process.env.MIN_AVERAGE_SCORE) || 0.75,
  
  // Retry settings
  MAX_VERIFICATION_RETRIES: parseInt(process.env.MAX_VERIFICATION_RETRIES) || 3,
  VERIFICATION_RETRY_DELAY: parseInt(process.env.VERIFICATION_RETRY_DELAY) || 5000,
  
  // Logging
  LOG_VERIFICATION_RESULTS: process.env.LOG_VERIFICATION_RESULTS !== 'false',
  DETAILED_LOGGING: process.env.DETAILED_VERIFICATION_LOGGING === 'true',

  // Debug mode - shows all API communications
  DEBUG_MODE: process.env.DEBUG_MODE === 'true',
  DEBUG_SHOW_REQUESTS: process.env.DEBUG_SHOW_REQUESTS === 'true',
  DEBUG_SHOW_RESPONSES: process.env.DEBUG_SHOW_RESPONSES === 'true',
  DEBUG_SHOW_PROMPTS: process.env.DEBUG_SHOW_PROMPTS === 'true',
  DEBUG_SKIP_TRANSLATION_PAYLOADS: process.env.DEBUG_SKIP_TRANSLATION_PAYLOADS !== 'false', // Default true - skips translation AND improvement payloads
  DEBUG_SKIP_TRANSLATION_PROMPTS: process.env.DEBUG_SKIP_TRANSLATION_PROMPTS !== 'false', // Default true - skips translation AND improvement prompts
  DEBUG_SKIP_VERIFICATION_PAYLOADS: process.env.DEBUG_SKIP_VERIFICATION_PAYLOADS !== 'false', // Default true - skips verification payloads
  DEBUG_SKIP_VERIFICATION_PROMPTS: process.env.DEBUG_SKIP_VERIFICATION_PROMPTS !== 'false', // Default true - skips verification prompts
  
  // Improvement tracking
  TRACK_IMPROVEMENTS: process.env.TRACK_IMPROVEMENTS !== 'false',
  IMPROVEMENT_LOG_PATH: process.env.IMPROVEMENT_LOG_PATH || 'app/logs/improvements.log',

  // JSON mode corrections (now the only mode)
  JSON_CORRECTION_MODEL: process.env.JSON_CORRECTION_MODEL || MODEL_CONFIG.IMPROVEMENT.MODEL,

  // Discord notifications for verification issues
  NOTIFY_VERIFICATION_ISSUES: process.env.NOTIFY_VERIFICATION_ISSUES === 'true',
  VERIFICATION_WEBHOOK: process.env.VERIFICATION_DISCORD_WEBHOOK || process.env.DEV_DISCORD_WEBHOOK,
};

export default { CLAUDE4_CONFIG, MODEL_CONFIG, AVAILABLE_MODELS };
