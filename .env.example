# =============================================================================
# API KEYS
# =============================================================================

ANTHROPIC_API_KEY=
PIEXLDRAIN_API_KEY=
PUBLIC_DISCORD_WEBHOOK=
DEV_DISCORD_WEBHOOK=
DISCORD_EMBED_WEBHOOK=
SPLENDOUR_CAFE_API_KEY=
SPLENDOUR_LOGIN=
SPLENDOUR_PASSWORD=
SUPABASE_ANON_KEY=
SUPABASE_PROJECT_URL=

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================

# Translation Model Configuration
# Model used for main translation tasks
# Available options: claude-3-5-sonnet-20241022, claude-3-5-sonnet-20240620, claude-3-opus-20240229, claude-3-sonnet-20240229, claude-3-haiku-20240307
TRANSLATION_MODEL=claude-3-5-sonnet-20241022
TRANSLATION_MAX_TOKENS=8192
TRANSLATION_TEMPERATURE=1.0

# Verification Model Configuration
# Model used for translation verification and quality analysis
# Recommended: claude-sonnet-4-20250514 (Claude 4 with tool use capabilities)
VERIFICATION_MODEL=claude-sonnet-4-20250514
VERIFICATION_MAX_TOKENS=8192
VERIFICATION_TEMPERATURE=0.3

# Improvement Model Configuration
# Model used for improving translations based on verification feedback
# Recommended: claude-3-5-sonnet-20241022 or claude-3-opus-20240229
IMPROVEMENT_MODEL=claude-3-5-sonnet-20241022
IMPROVEMENT_MAX_TOKENS=8192
IMPROVEMENT_TEMPERATURE=0.7

# =============================================================================
# VERIFICATION SYSTEM CONFIGURATION
# =============================================================================

# Enable/disable Claude 4 verification system
CLAUDE4_VERIFICATION_ENABLED=true

# Verification mode: 'full', 'sample', or 'disabled'
# - full: Verify every chunk
# - sample: Verify a percentage of chunks (see CLAUDE4_SAMPLE_RATE)
# - disabled: No verification
CLAUDE4_VERIFICATION_MODE=full

# Sample rate for verification when in 'sample' mode (0.0 to 1.0)
CLAUDE4_SAMPLE_RATE=0.2

# Quality threshold - single condition based on average of all scores (0.0 to 1.0)
# If average score is below this threshold, verification fails and improvement is attempted
MIN_AVERAGE_SCORE=0.75

# Verification retry settings
MAX_VERIFICATION_RETRIES=3
VERIFICATION_RETRY_DELAY=5000

# Logging configuration
LOG_VERIFICATION_RESULTS=true
DETAILED_VERIFICATION_LOGGING=false

# Improvement tracking
TRACK_IMPROVEMENTS=true
IMPROVEMENT_LOG_PATH=app/logs/improvements.log

# Discord notifications for verification issues
NOTIFY_VERIFICATION_ISSUES=false
VERIFICATION_DISCORD_WEBHOOK=

# =============================================================================
# DEBUG CONFIGURATION
# =============================================================================

# Enable debug mode for detailed API communications
DEBUG_MODE=false

# Show specific types of debug information
DEBUG_SHOW_REQUESTS=false
DEBUG_SHOW_RESPONSES=false
DEBUG_SHOW_PROMPTS=false

# Skip large payloads and prompts for translation and improvement tasks (recommended: true)
# These can be very large and clutter the debug output
DEBUG_SKIP_TRANSLATION_PAYLOADS=true
DEBUG_SKIP_TRANSLATION_PROMPTS=true

# Skip large payloads and prompts for verification tasks (recommended: true)
# Verification prompts and payloads can be very large and clutter the debug output
DEBUG_SKIP_VERIFICATION_PAYLOADS=true
DEBUG_SKIP_VERIFICATION_PROMPTS=true