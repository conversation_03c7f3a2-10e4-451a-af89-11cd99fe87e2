import fs from 'fs'

// Function to normalize timecodes in an .ass file
function normalizeTimecodes(inputFile, outputFile) {
    const content = fs.readFileSync(inputFile, 'utf8');
    const lines = content.split('\n');

    let firstTimecode = null;
    const normalizedLines = lines.map(line => {
        if (line.startsWith('Dialogue:')) {
            const parts = line.split(',');
            if (parts.length >= 2) {
                const startTime = parseTimecode(parts[1]);
                const endTime = parseTimecode(parts[2]);

                if (firstTimecode === null) {
                    firstTimecode = startTime;
                }

                const normalizedStart = formatTimecode(startTime - firstTimecode);
                const normalizedEnd = formatTimecode(endTime - firstTimecode);

                parts[1] = normalizedStart;
                parts[2] = normalizedEnd;

                return parts.join(',');
            }
        }
        return line;
    });

    fs.writeFileSync(outputFile, normalizedLines.join('\n'));
    console.log(`Normalized timecodes saved to ${outputFile}`);
}

// Helper function to parse timecode string to milliseconds
function parseTimecode(timecode) {
    const [hours, minutes, seconds] = timecode.split(':');
    return (parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds)) * 1000;
}

// Helper function to format milliseconds to timecode string
function formatTimecode(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = (totalSeconds % 60) + (ms % 1000) / 1000;
    return `${padZero(hours)}:${padZero(minutes)}:${padZero(seconds.toFixed(2))}`;
}

// Helper function to pad single digits with leading zero
function padZero(num) {
    return num.toString().padStart(2, '0');
}

normalizeTimecodes('app/karaoketest.ass', 'app/normalized.ass');
