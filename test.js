import fs from 'fs/promises';
import { XMLParser } from 'fast-xml-parser';
import path from 'path';

// Function to convert XML to CSV
async function convertXMLtoCSV(inputFile, outputFile) {
  try {
    // Read the XML file
    const data = await fs.readFile(inputFile, 'utf8');

    // Parse XML
    const parser = new XMLParser({
      ignoreAttributes: false,
      isArray: (name) => name === 'example'
    });
    const result = parser.parse(data);

    // Extract the examples
    const examples = result.examples.example;

    // Create CSV content
    let csvContent = 'input:,output:\n';

    examples.forEach(example => {
      // Escape quotes in the content by doubling them
      const source = example.English_Source.replace(/"/g, '""');
      const output = example.ideal_output.replace(/"/g, '""');

      // Add quotes around fields to handle commas and newlines correctly
      csvContent += `"${source}","${output}"\n`;
    });

    // Write to CSV file
    await fs.writeFile(outputFile, csvContent);
    console.log(`Successfully converted XML to CSV and saved to ${outputFile}`);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Get command line arguments
const args = process.argv.slice(2);
const inputFile = args[0] || 'examples.xml';
const outputFile = args[1] || 'result.csv';

console.log(`Converting from ${inputFile} to ${outputFile}...`);
convertXMLtoCSV(inputFile, outputFile);